import * as fs from 'fs';
import * as path from 'path';
import GoogleVisionUtil from './googleVisionUtil.ts';
import logger from './logger.util.ts';

/**
 * Utility class for OCR-based text detection and clicking
 */
class OcrClickUtil {
  private visionUtil: GoogleVisionUtil;

  constructor() {
    this.visionUtil = new GoogleVisionUtil();
  }

  /**
   * Clicks on text found in the current screen
   * @param textToFind - The text to find and click on
   * @param maxAttempts - Maximum number of attempts to find the text (default: 3)
   * @param waitBetweenAttempts - Time to wait between attempts in ms (default: 1000)
   * @returns Promise<boolean> - True if text was found and clicked, false otherwise
   */
  public async clickOnText(
    textToFind: string,
    maxAttempts: number = 3,
    waitBetweenAttempts: number = 1000,
  ): Promise<boolean> {
    logger.info(`Attempting to find and click on text: "${textToFind}"`);
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        logger.info(`Attempt ${attempt}/${maxAttempts} to find text: "${textToFind}"`);
        
        // Take screenshot of the current screen
        const timestamp = new Date().getTime();
        const screenshotName = `ocr_click_${timestamp}.png`;
        const screenshotPath = path.resolve(process.cwd(), 'reports/screenshots', screenshotName);
        
        // Ensure directory exists
        const dir = path.dirname(screenshotPath);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }
        
        // Take screenshot and save it
        await browser.saveScreenshot(screenshotPath);
        logger.info(`Screenshot saved to: ${screenshotPath}`);
        
        // Use OCR to detect text in the screenshot
        const detectedText = await this.visionUtil.detectText(screenshotPath);
        logger.info(`Detected text snippet: "${detectedText.substring(0, 100)}..."`);
        
        // Find the text in the detected text
        if (detectedText.includes(textToFind)) {
          logger.info(`Found "${textToFind}" in the screenshot`);
          
          // Get window size to calculate relative positions
          const { width, height } = await browser.getWindowSize();
          
          // Since we don't have exact coordinates from OCR, we'll use a heuristic approach
          // This could be improved by enhancing GoogleVisionUtil to return bounding boxes
          
          // Try to click in the center of the screen where the text likely is
          // For mobile devices, use touchAction
          if (browser.isMobile) {
            await browser.touchAction([
              { action: 'tap', x: Math.floor(width / 2), y: Math.floor(height / 2) },
            ]);
            logger.info(`Tapped center of screen where "${textToFind}" likely is`);
          } else {
            // For desktop, use mouse click
            await browser.performActions([{
              type: 'pointer',
              id: 'mouse',
              parameters: { pointerType: 'mouse' },
              actions: [
                { type: 'pointerMove', duration: 0, x: Math.floor(width / 2), y: Math.floor(height / 2) },
                { type: 'pointerDown', button: 0 },
                { type: 'pointerUp', button: 0 },
              ],
            }]);
            logger.info(`Clicked center of screen where "${textToFind}" likely is`);
          }
          
          // Try alternative JavaScript approach as well
          try {
            await browser.execute(`
              // Try to find elements containing the text
              const elements = Array.from(document.querySelectorAll('*'))
                .filter(el => el.textContent && el.textContent.includes('${textToFind}') && 
                       (window.getComputedStyle(el).display !== 'none'));
              
              // Click the first visible element found
              if (elements.length > 0) {
                elements[0].click();
                return true;
              }
              return false;
            `);
          } catch (jsError) {
            logger.info(`JavaScript click approach failed: ${jsError}`);
          }
          
          return true;
        } else {
          logger.info(`Could not find "${textToFind}" in attempt ${attempt}`);
          
          if (attempt < maxAttempts) {
            logger.info(`Waiting ${waitBetweenAttempts}ms before next attempt`);
            await browser.pause(waitBetweenAttempts);
          }
        }
      } catch (error) {
        logger.error(`Error in attempt ${attempt} to find text: ${error}`);
        
        if (attempt < maxAttempts) {
          logger.info(`Waiting ${waitBetweenAttempts}ms before next attempt`);
          await browser.pause(waitBetweenAttempts);
        }
      }
    }
    
    logger.error(`Failed to find and click on text "${textToFind}" after ${maxAttempts} attempts`);
    return false;
  }
}

export default new OcrClickUtil();